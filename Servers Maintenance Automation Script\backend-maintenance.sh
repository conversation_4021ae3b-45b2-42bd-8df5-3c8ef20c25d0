#!/bin/bash

# ============================================================================
# StreamDB Backend Server Automated Maintenance Script
# Server: backend1maindb (***********)
# Purpose: Weekly maintenance with service protection
# Schedule: Every Thursday at 00:00 (midnight)
# ============================================================================

set -euo pipefail  # Exit on error, undefined vars, pipe failures

# Configuration
SCRIPT_NAME="StreamDB Backend Maintenance"
SCRIPT_VERSION="1.0"
LOG_DIR="/var/log/streamdb-maintenance"
LOG_FILE="$LOG_DIR/backend-maintenance-$(date +%Y%m%d-%H%M%S).log"
BACKUP_DIR="/var/backups/streamdb-maintenance"
LOCK_FILE="/var/run/streamdb-backend-maintenance.lock"
MAX_LOG_FILES=30
NOTIFICATION_EMAIL=""  # Set if you want email notifications

# Service paths and configurations
STREAMDB_PATH="/var/www/streamdb_onl_usr/data/www/streamdb.online"
PM2_APP_NAME="streamdb-online"
MYSQL_DB="streamdb_database"
MYSQL_USER="dbadmin_streamdb"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================

log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${timestamp} [${level}] ${message}" | tee -a "$LOG_FILE"
}

log_info() { log "INFO" "$@"; }
log_warn() { log "WARN" "$@"; }
log_error() { log "ERROR" "$@"; }
log_success() { log "SUCCESS" "$@"; }

print_header() {
    echo -e "${BLUE}============================================================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}============================================================================${NC}"
    log_info "$1"
}

print_section() {
    echo -e "\n${YELLOW}--- $1 ---${NC}"
    log_info "Starting: $1"
}

check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "This script must be run as root"
        exit 1
    fi
}

create_lock() {
    if [[ -f "$LOCK_FILE" ]]; then
        local pid=$(cat "$LOCK_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            log_error "Maintenance script is already running (PID: $pid)"
            exit 1
        else
            log_warn "Removing stale lock file"
            rm -f "$LOCK_FILE"
        fi
    fi
    echo $$ > "$LOCK_FILE"
    log_info "Created lock file: $LOCK_FILE"
}

remove_lock() {
    rm -f "$LOCK_FILE"
    log_info "Removed lock file"
}

setup_directories() {
    mkdir -p "$LOG_DIR" "$BACKUP_DIR"
    chmod 755 "$LOG_DIR" "$BACKUP_DIR"
    log_info "Created maintenance directories"
}

cleanup_old_logs() {
    find "$LOG_DIR" -name "backend-maintenance-*.log" -type f -mtime +$MAX_LOG_FILES -delete 2>/dev/null || true
    find "$BACKUP_DIR" -name "*.tar.gz" -type f -mtime +7 -delete 2>/dev/null || true
    log_info "Cleaned up old log files and backups"
}

# ============================================================================
# SERVICE STATUS FUNCTIONS
# ============================================================================

check_service_status() {
    local service="$1"
    if systemctl is-active --quiet "$service"; then
        log_info "Service $service is running"
        return 0
    else
        log_warn "Service $service is not running"
        return 1
    fi
}

check_pm2_status() {
    if pm2 list | grep -q "$PM2_APP_NAME.*online"; then
        log_info "PM2 application $PM2_APP_NAME is running"
        return 0
    else
        log_warn "PM2 application $PM2_APP_NAME is not running"
        return 1
    fi
}

get_service_states() {
    declare -gA SERVICE_STATES
    SERVICE_STATES[nginx]=$(systemctl is-active nginx 2>/dev/null || echo "inactive")
    SERVICE_STATES[mysql]=$(systemctl is-active mysql 2>/dev/null || echo "inactive")
    SERVICE_STATES[fastpanel]=$(systemctl is-active fastpanel 2>/dev/null || echo "inactive")
    SERVICE_STATES[pm2]=$(pm2 list | grep -q "$PM2_APP_NAME.*online" && echo "active" || echo "inactive")
    
    log_info "Captured service states: nginx=${SERVICE_STATES[nginx]}, mysql=${SERVICE_STATES[mysql]}, fastpanel=${SERVICE_STATES[fastpanel]}, pm2=${SERVICE_STATES[pm2]}"
}

restore_service_states() {
    log_info "Restoring service states..."
    
    # Restore services that were running
    for service in nginx mysql fastpanel; do
        if [[ "${SERVICE_STATES[$service]}" == "active" ]]; then
            if ! systemctl is-active --quiet "$service"; then
                log_warn "Restarting $service (was running before maintenance)"
                systemctl start "$service" || log_error "Failed to restart $service"
            fi
        fi
    done
    
    # Restore PM2 application
    if [[ "${SERVICE_STATES[pm2]}" == "active" ]]; then
        if ! pm2 list | grep -q "$PM2_APP_NAME.*online"; then
            log_warn "Restarting PM2 application $PM2_APP_NAME"
            cd "$STREAMDB_PATH/server" && pm2 restart "$PM2_APP_NAME" || log_error "Failed to restart PM2 application"
        fi
    fi
}

# ============================================================================
# BACKUP FUNCTIONS
# ============================================================================

create_config_backup() {
    print_section "Creating Configuration Backup"
    
    local backup_file="$BACKUP_DIR/config-backup-$(date +%Y%m%d-%H%M%S).tar.gz"
    
    # Backup critical configuration files
    tar -czf "$backup_file" \
        /etc/nginx/sites-available/ \
        /etc/nginx/sites-enabled/ \
        /etc/nginx/nginx.conf \
        /etc/mysql/mysql.conf.d/ \
        /etc/fastpanel/ \
        "$STREAMDB_PATH/server/.env" \
        /etc/crontab \
        /var/spool/cron/crontabs/ \
        2>/dev/null || true
    
    if [[ -f "$backup_file" ]]; then
        log_success "Configuration backup created: $backup_file"
        return 0
    else
        log_error "Failed to create configuration backup"
        return 1
    fi
}

create_database_backup() {
    print_section "Creating Database Backup"
    
    local backup_file="$BACKUP_DIR/mysql-backup-$(date +%Y%m%d-%H%M%S).sql.gz"
    
    # Create database backup
    if mysqldump --single-transaction --routines --triggers "$MYSQL_DB" | gzip > "$backup_file"; then
        log_success "Database backup created: $backup_file"
        return 0
    else
        log_error "Failed to create database backup"
        return 1
    fi
}

# ============================================================================
# UPDATE FUNCTIONS
# ============================================================================

update_package_lists() {
    print_section "Updating Package Lists"
    
    if apt update; then
        log_success "Package lists updated successfully"
        return 0
    else
        log_error "Failed to update package lists"
        return 1
    fi
}

apply_security_updates() {
    print_section "Applying Security Updates"
    
    # Get list of security updates
    local security_updates=$(apt list --upgradable 2>/dev/null | grep -i security | wc -l)
    
    if [[ $security_updates -gt 0 ]]; then
        log_info "Found $security_updates security updates to apply"
        
        # Apply only security updates
        if unattended-upgrade -d; then
            log_success "Security updates applied successfully"
            return 0
        else
            log_error "Failed to apply security updates"
            return 1
        fi
    else
        log_info "No security updates available"
        return 0
    fi
}

apply_safe_os_updates() {
    print_section "Applying Safe OS Updates"
    
    # Get list of available updates (excluding major version changes)
    local available_updates=$(apt list --upgradable 2>/dev/null | grep -v "WARNING" | wc -l)
    
    if [[ $available_updates -gt 0 ]]; then
        log_info "Found $available_updates updates available"
        
        # Apply updates with safety measures
        DEBIAN_FRONTEND=noninteractive apt-get -y \
            -o Dpkg::Options::="--force-confdef" \
            -o Dpkg::Options::="--force-confold" \
            --no-install-recommends \
            upgrade
        
        if [[ $? -eq 0 ]]; then
            log_success "OS updates applied successfully"
            return 0
        else
            log_error "Failed to apply OS updates"
            return 1
        fi
    else
        log_info "No OS updates available"
        return 0
    fi
}

# ============================================================================
# CLEANUP FUNCTIONS
# ============================================================================

system_cleanup() {
    print_section "Performing System Cleanup"
    
    # Clean package cache
    apt-get clean
    apt-get autoclean
    log_info "Cleaned package cache"
    
    # Remove unused packages
    apt-get -y autoremove
    log_info "Removed unused packages"
    
    # Clean temporary files
    find /tmp -type f -atime +7 -delete 2>/dev/null || true
    find /var/tmp -type f -atime +7 -delete 2>/dev/null || true
    log_info "Cleaned temporary files"
    
    # Rotate and clean logs
    logrotate -f /etc/logrotate.conf 2>/dev/null || true
    log_info "Rotated system logs"
    
    # Clean old kernels (keep current + 1 previous)
    local old_kernels=$(dpkg -l | grep linux-image | grep -v $(uname -r) | awk '{print $2}' | head -n -1)
    if [[ -n "$old_kernels" ]]; then
        echo "$old_kernels" | xargs apt-get -y purge 2>/dev/null || true
        log_info "Cleaned old kernel versions"
    fi
    
    log_success "System cleanup completed"
}

clean_application_logs() {
    print_section "Cleaning Application Logs"
    
    # Clean PM2 logs
    pm2 flush "$PM2_APP_NAME" 2>/dev/null || true
    log_info "Cleaned PM2 logs"
    
    # Clean Nginx logs older than 30 days
    find /var/log/nginx/ -name "*.log" -type f -mtime +30 -delete 2>/dev/null || true
    log_info "Cleaned old Nginx logs"
    
    # Clean MySQL logs older than 7 days
    find /var/log/mysql/ -name "*.log" -type f -mtime +7 -delete 2>/dev/null || true
    log_info "Cleaned old MySQL logs"
    
    # Clean StreamDB application logs
    if [[ -d "$STREAMDB_PATH/logs" ]]; then
        find "$STREAMDB_PATH/logs" -name "*.log" -type f -mtime +14 -delete 2>/dev/null || true
        log_info "Cleaned old StreamDB application logs"
    fi
    
    log_success "Application log cleanup completed"
}

# ============================================================================
# HEALTH CHECK FUNCTIONS
# ============================================================================

health_check() {
    print_section "Performing Health Check"
    
    local health_status=0
    
    # Check critical services
    for service in nginx mysql fastpanel; do
        if ! check_service_status "$service"; then
            health_status=1
        fi
    done
    
    # Check PM2 application
    if ! check_pm2_status; then
        health_status=1
    fi
    
    # Check disk space
    local disk_usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    if [[ $disk_usage -gt 85 ]]; then
        log_warn "Disk usage is high: ${disk_usage}%"
        health_status=1
    else
        log_info "Disk usage is acceptable: ${disk_usage}%"
    fi
    
    # Check memory usage
    local mem_usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    if [[ $mem_usage -gt 90 ]]; then
        log_warn "Memory usage is high: ${mem_usage}%"
        health_status=1
    else
        log_info "Memory usage is acceptable: ${mem_usage}%"
    fi
    
    # Test database connectivity
    if mysql -u "$MYSQL_USER" -p"$(grep DB_PASSWORD $STREAMDB_PATH/server/.env | cut -d'=' -f2)" -e "SELECT 1;" "$MYSQL_DB" >/dev/null 2>&1; then
        log_info "Database connectivity test passed"
    else
        log_error "Database connectivity test failed"
        health_status=1
    fi
    
    # Test web application
    if curl -s -o /dev/null -w "%{http_code}" http://localhost/api/health | grep -q "200"; then
        log_info "Web application health check passed"
    else
        log_warn "Web application health check failed"
        health_status=1
    fi
    
    if [[ $health_status -eq 0 ]]; then
        log_success "All health checks passed"
        return 0
    else
        log_warn "Some health checks failed"
        return 1
    fi
}

# ============================================================================
# MAIN EXECUTION
# ============================================================================

main() {
    # Setup
    check_root
    setup_directories
    create_lock
    
    # Trap to ensure cleanup on exit
    trap 'remove_lock; exit' INT TERM EXIT
    
    print_header "$SCRIPT_NAME v$SCRIPT_VERSION - $(date)"
    log_info "Starting maintenance on backend1maindb (***********)"
    
    # Capture current service states
    get_service_states
    
    # Create backups
    if ! create_config_backup; then
        log_error "Configuration backup failed - aborting maintenance"
        exit 1
    fi
    
    if ! create_database_backup; then
        log_error "Database backup failed - aborting maintenance"
        exit 1
    fi
    
    # Perform updates
    update_package_lists
    apply_security_updates
    apply_safe_os_updates
    
    # Perform cleanup
    system_cleanup
    clean_application_logs
    cleanup_old_logs
    
    # Restore services if needed
    restore_service_states
    
    # Final health check
    sleep 10  # Give services time to stabilize
    health_check
    
    print_header "Maintenance Completed Successfully"
    log_success "Backend server maintenance completed at $(date)"
    
    # Remove lock file
    remove_lock
}

# Execute main function
main "$@"
