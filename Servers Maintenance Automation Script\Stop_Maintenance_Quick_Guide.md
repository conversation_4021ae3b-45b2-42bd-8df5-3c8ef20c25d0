# 🛑 StreamDB Maintenance Scripts - Stop/Disable Guide

## 📋 OVERVIEW

This guide shows you how to stop, disable, or completely remove the automated maintenance scripts from your StreamDB infrastructure when needed.

**Script:** `stop-maintenance-scripts.sh`  
**Purpose:** Safely stop and disable automated maintenance  
**Safety:** Preserves backups and provides restoration options

---

## 🚀 QUICK COMMANDS

### Check Current Status
```bash
# Upload the stop script to your server first
scp stop-maintenance-scripts.sh root@***********:/usr/local/bin/
scp stop-maintenance-scripts.sh root@*************:/usr/local/bin/
chmod +x /usr/local/bin/stop-maintenance-scripts.sh

# Check what's currently running
sudo /usr/local/bin/stop-maintenance-scripts.sh --status
```

### Stop Running Maintenance (Keep Scheduled)
```bash
# Just stop current maintenance, keep cron jobs
sudo /usr/local/bin/stop-maintenance-scripts.sh --stop
```

### Temporarily Disable (Reversible)
```bash
# Stop maintenance and disable cron jobs (scripts preserved)
sudo /usr/local/bin/stop-maintenance-scripts.sh --disable
```

### Permanently Disable (Remove Everything)
```bash
# Stop, disable, backup, and remove all scripts
sudo /usr/local/bin/stop-maintenance-scripts.sh --disable-permanently
```

### Emergency Stop (Immediate)
```bash
# Immediately terminate everything (use only if stuck)
sudo /usr/local/bin/stop-maintenance-scripts.sh --emergency
```

---

## 📊 DETAILED OPTIONS

### 1. **Status Check** (`--status`)
**What it does:**
- Shows running maintenance processes
- Lists active cron jobs
- Checks for lock files
- Displays script locations
- Shows disable markers

**When to use:**
- Before making any changes
- To troubleshoot issues
- To verify current state

**Example output:**
```
Server type: backend
No maintenance processes running
No maintenance cron jobs found
Found 4 maintenance script(s)
No disable markers found
```

### 2. **Stop Only** (`--stop`)
**What it does:**
- Stops currently running maintenance
- Removes lock files
- Keeps cron jobs active
- Preserves all scripts

**When to use:**
- Maintenance is stuck or taking too long
- Need to stop current run but keep scheduling
- Temporary intervention needed

**Result:**
- Current maintenance stopped
- Next scheduled run will still occur
- All scripts remain in place

### 3. **Temporary Disable** (`--disable`)
**What it does:**
- Stops running maintenance
- Disables all cron jobs
- Keeps scripts in place
- Creates disable marker

**When to use:**
- Need to pause maintenance for extended period
- System maintenance by you
- Troubleshooting issues
- Want easy re-enabling later

**Re-enabling:**
```bash
# To re-enable later
sudo /usr/local/bin/cron-configuration.sh
sudo rm -f /var/lib/streamdb-maintenance-disabled
```

### 4. **Permanent Disable** (`--disable-permanently`)
**What it does:**
- Stops running maintenance
- Disables all cron jobs
- Backs up all scripts
- Removes scripts from system
- Creates permanent marker

**When to use:**
- No longer want automated maintenance
- Switching to manual maintenance
- Decommissioning automation

**Backup location:**
```
/var/backups/streamdb-maintenance-disabled-YYYYMMDD-HHMMSS/
```

**Restoration:**
```bash
# To restore from backup
cp /var/backups/streamdb-maintenance-disabled-*/*.sh /usr/local/bin/
chmod +x /usr/local/bin/*.sh
/usr/local/bin/cron-configuration.sh
```

### 5. **Emergency Stop** (`--emergency`)
**What it does:**
- Immediately kills all maintenance processes
- Removes all lock files
- Removes all cron jobs
- Restarts cron service

**When to use:**
- Maintenance is completely stuck
- System is unresponsive due to maintenance
- Critical emergency situation

**⚠️ Warning:**
- May leave services in inconsistent state
- Use only as last resort
- Check services manually after use

---

## 🔧 USAGE SCENARIOS

### Scenario 1: Maintenance Running Too Long
```bash
# Check what's happening
sudo /usr/local/bin/stop-maintenance-scripts.sh --status

# Stop current run (will resume next Thursday)
sudo /usr/local/bin/stop-maintenance-scripts.sh --stop
```

### Scenario 2: Need to Pause for Server Work
```bash
# Temporarily disable (easy to re-enable)
sudo /usr/local/bin/stop-maintenance-scripts.sh --disable

# Do your server work...

# Re-enable when done
sudo /usr/local/bin/cron-configuration.sh
```

### Scenario 3: No Longer Want Automation
```bash
# Permanently remove (with backup)
sudo /usr/local/bin/stop-maintenance-scripts.sh --disable-permanently
```

### Scenario 4: System Emergency
```bash
# Emergency stop everything
sudo /usr/local/bin/stop-maintenance-scripts.sh --emergency

# Then check services manually
systemctl status nginx mysql fastpanel
pm2 status
```

---

## 📁 FILE LOCATIONS

### Scripts
```
/usr/local/bin/backend-maintenance.sh          # Backend maintenance
/usr/local/bin/reverse-proxy-maintenance.sh    # Proxy maintenance
/usr/local/bin/validation-scripts.sh           # Validation
/usr/local/bin/cron-configuration.sh           # Cron setup
/usr/local/bin/stop-maintenance-scripts.sh     # Stop script
```

### Lock Files
```
/var/run/streamdb-backend-maintenance.lock     # Backend lock
/var/run/streamdb-proxy-maintenance.lock       # Proxy lock
```

### Cron Configuration
```
/etc/cron.d/streamdb-maintenance-env           # Cron environment
```

### Markers
```
/var/lib/streamdb-maintenance-disabled         # Temporary disable
/var/lib/streamdb-maintenance-permanently-disabled  # Permanent disable
```

### Backups
```
/var/backups/streamdb-maintenance-disabled-*/  # Script backups
```

---

## 🔍 TROUBLESHOOTING

### Issue 1: Script Won't Stop
```bash
# Try emergency stop
sudo /usr/local/bin/stop-maintenance-scripts.sh --emergency

# Manually kill processes
sudo pkill -KILL -f maintenance

# Remove lock files
sudo rm -f /var/run/streamdb-*-maintenance.lock
```

### Issue 2: Cron Jobs Still Running
```bash
# Check cron jobs
crontab -l

# Manually remove
crontab -e
# Delete maintenance lines

# Or remove all
crontab -r
```

### Issue 3: Can't Re-enable After Disable
```bash
# Check if scripts exist
ls -la /usr/local/bin/*maintenance.sh

# If missing, restore from backup
ls -la /var/backups/streamdb-maintenance-disabled-*/
cp /var/backups/streamdb-maintenance-disabled-*/*.sh /usr/local/bin/
chmod +x /usr/local/bin/*.sh

# Re-configure cron
/usr/local/bin/cron-configuration.sh
```

### Issue 4: Services Broken After Emergency Stop
```bash
# Restart all services
sudo systemctl restart nginx mysql fastpanel
sudo pm2 restart streamdb-online

# Check website
curl -I https://streamdb.online
```

---

## ⚠️ SAFETY REMINDERS

### Before Stopping Maintenance
1. **Check status first**: Always run `--status` to understand current state
2. **Choose right option**: Use least disruptive option for your needs
3. **Consider timing**: Don't stop during critical operations

### After Stopping Maintenance
1. **Verify services**: Check that all services are still running
2. **Test website**: Ensure website functionality is intact
3. **Monitor system**: Watch for any issues from interrupted maintenance

### Re-enabling Considerations
1. **System state**: Ensure system is stable before re-enabling
2. **Timing**: Consider when next maintenance will run
3. **Validation**: Run validation scripts before re-enabling

---

## 📞 EMERGENCY CONTACTS

### If Something Goes Wrong
1. **Check services immediately**:
   ```bash
   systemctl status nginx mysql fastpanel
   pm2 status
   curl -I https://streamdb.online
   ```

2. **Restart services if needed**:
   ```bash
   systemctl restart nginx mysql fastpanel
   pm2 restart streamdb-online
   ```

3. **Check logs for errors**:
   ```bash
   tail -f /var/log/nginx/error.log
   pm2 logs streamdb-online
   journalctl -f
   ```

### Recovery Steps
1. Stop all maintenance processes
2. Remove lock files
3. Restart affected services
4. Verify website functionality
5. Check system logs for errors

---

**Quick Guide Version:** 1.0  
**Last Updated:** 2025-01-02  
**For:** StreamDB.online Maintenance Management
