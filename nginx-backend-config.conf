# StreamDB Backend Nginx Configuration
# Deploy to: /etc/nginx/sites-available/streamdb-backend on ***********

server {
    listen 80;
    server_name streamdb.online www.streamdb.online ***********;
    
    # Document root for static files (React build)
    root /var/www/streamdb_onl_usr/data/www/streamdb.online/dist;
    index index.html;
    
    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    
    # Trust reverse proxy headers
    real_ip_header X-Forwarded-For;
    set_real_ip_from *************;
    
    # Gzip Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json
        image/svg+xml;
    
    # API Routes - Proxy to Node.js backend
    location /api/ {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Timeouts
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # Buffer settings
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
    }
    
    # Upload endpoint with larger body size
    location /api/upload {
        client_max_body_size 50M;
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Extended timeouts for uploads
        proxy_connect_timeout 60s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
    }
    
    # Serve uploaded files
    location /uploads/ {
        alias /var/www/streamdb_onl_usr/data/www/streamdb.online/uploads/;
        expires 1y;
        add_header Cache-Control "public, immutable";
        
        # Security for uploaded files
        location ~* \.(php|php5|phtml|pl|py|jsp|asp|sh|cgi)$ {
            deny all;
        }
    }
    
    # Static assets with long cache
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|webp)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        
        # Try files in dist directory first, then uploads
        try_files $uri $uri/ @fallback;
    }
    
    # Fallback for missing static files
    location @fallback {
        try_files $uri $uri/ /index.html;
    }
    
    # Admin panel routes
    location /admin {
        try_files $uri $uri/ /index.html;
        
        # No cache for admin pages
        expires -1;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
    }
    
    # SPA Fallback - Serve index.html for all routes
    location / {
        try_files $uri $uri/ /index.html;
        
        # No cache for HTML files
        location ~* \.html$ {
            expires -1;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
        }
    }
    
    # Health check endpoint
    location = /health {
        proxy_pass http://localhost:3001/api/health;
        access_log off;
    }
    
    # Robots.txt
    location = /robots.txt {
        log_not_found off;
        access_log off;
    }
    
    # Favicon
    location = /favicon.ico {
        log_not_found off;
        access_log off;
        expires 1y;
    }
    
    # Security: Block access to sensitive files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ /(\.env|\.git|node_modules|server|database|logs) {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # Block access to backup files
    location ~ \.(bak|backup|old|tmp|temp)$ {
        deny all;
    }
    
    # Logging
    access_log /var/log/nginx/streamdb_backend_access.log;
    error_log /var/log/nginx/streamdb_backend_error.log;
}

# HTTPS redirect (when SSL is configured)
server {
    listen 443 ssl http2;
    server_name streamdb.online www.streamdb.online;
    
    # For now, redirect HTTPS to HTTP since reverse proxy handles SSL
    return 301 http://$server_name$request_uri;
}
