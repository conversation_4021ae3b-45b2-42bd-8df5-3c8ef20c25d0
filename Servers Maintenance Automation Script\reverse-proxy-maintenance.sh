#!/bin/bash

# ============================================================================
# StreamDB Reverse Proxy Server Automated Maintenance Script
# Server: backend2ndrevproxy (*************)
# Purpose: Weekly maintenance with reverse proxy protection
# Schedule: Every Thursday at 00:30 (30 minutes after backend)
# ============================================================================

set -euo pipefail  # Exit on error, undefined vars, pipe failures

# Configuration
SCRIPT_NAME="StreamDB Reverse Proxy Maintenance"
SCRIPT_VERSION="1.0"
LOG_DIR="/var/log/streamdb-maintenance"
LOG_FILE="$LOG_DIR/proxy-maintenance-$(date +%Y%m%d-%H%M%S).log"
BACKUP_DIR="/var/backups/streamdb-maintenance"
LOCK_FILE="/var/run/streamdb-proxy-maintenance.lock"
MAX_LOG_FILES=30
NOTIFICATION_EMAIL=""  # Set if you want email notifications

# Backend server details
BACKEND_SERVER="***********"
BACKEND_HEALTH_URL="http://${BACKEND_SERVER}/api/health"

# Nginx configuration paths
NGINX_SITES_AVAILABLE="/etc/nginx/sites-available"
NGINX_SITES_ENABLED="/etc/nginx/sites-enabled"
NGINX_MAIN_CONFIG="/etc/nginx/nginx.conf"
STREAMDB_NGINX_CONFIG="$NGINX_SITES_AVAILABLE/streamdb.online"

# SSL certificate paths
SSL_CERT_PATH="/etc/ssl/certs/cloudflare-origin.pem"
SSL_KEY_PATH="/etc/ssl/private/cloudflare-origin.key"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================

log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${timestamp} [${level}] ${message}" | tee -a "$LOG_FILE"
}

log_info() { log "INFO" "$@"; }
log_warn() { log "WARN" "$@"; }
log_error() { log "ERROR" "$@"; }
log_success() { log "SUCCESS" "$@"; }

print_header() {
    echo -e "${BLUE}============================================================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}============================================================================${NC}"
    log_info "$1"
}

print_section() {
    echo -e "\n${YELLOW}--- $1 ---${NC}"
    log_info "Starting: $1"
}

check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "This script must be run as root"
        exit 1
    fi
}

create_lock() {
    if [[ -f "$LOCK_FILE" ]]; then
        local pid=$(cat "$LOCK_FILE")
        if kill -0 "$pid" 2>/dev/null; then
            log_error "Maintenance script is already running (PID: $pid)"
            exit 1
        else
            log_warn "Removing stale lock file"
            rm -f "$LOCK_FILE"
        fi
    fi
    echo $$ > "$LOCK_FILE"
    log_info "Created lock file: $LOCK_FILE"
}

remove_lock() {
    rm -f "$LOCK_FILE"
    log_info "Removed lock file"
}

setup_directories() {
    mkdir -p "$LOG_DIR" "$BACKUP_DIR"
    chmod 755 "$LOG_DIR" "$BACKUP_DIR"
    log_info "Created maintenance directories"
}

cleanup_old_logs() {
    find "$LOG_DIR" -name "proxy-maintenance-*.log" -type f -mtime +$MAX_LOG_FILES -delete 2>/dev/null || true
    find "$BACKUP_DIR" -name "*.tar.gz" -type f -mtime +7 -delete 2>/dev/null || true
    log_info "Cleaned up old log files and backups"
}

cleanup_old_backups_and_logs() {
    print_section "Cleaning Old Backup and Log Files (2+ weeks)"

    local files_removed=0

    # Clean maintenance backup files older than 2 weeks (14 days)
    local old_backups=$(find "$BACKUP_DIR" -name "proxy-config-backup-*.tar.gz" -type f -mtime +14 2>/dev/null | wc -l)
    if [[ $old_backups -gt 0 ]]; then
        find "$BACKUP_DIR" -name "proxy-config-backup-*.tar.gz" -type f -mtime +14 -delete 2>/dev/null || true
        log_info "Removed $old_backups old proxy configuration backup files (>14 days)"
        files_removed=$((files_removed + old_backups))
    fi

    # Clean any other backup files older than 2 weeks
    local old_other_backups=$(find "$BACKUP_DIR" -name "*.tar.gz" -type f -mtime +14 2>/dev/null | wc -l)
    if [[ $old_other_backups -gt 0 ]]; then
        find "$BACKUP_DIR" -name "*.tar.gz" -type f -mtime +14 -delete 2>/dev/null || true
        log_info "Removed $old_other_backups old backup files (>14 days)"
        files_removed=$((files_removed + old_other_backups))
    fi

    # Clean maintenance log files older than 2 weeks
    local old_maintenance_logs=$(find "$LOG_DIR" -name "proxy-maintenance-*.log" -type f -mtime +14 2>/dev/null | wc -l)
    if [[ $old_maintenance_logs -gt 0 ]]; then
        find "$LOG_DIR" -name "proxy-maintenance-*.log" -type f -mtime +14 -delete 2>/dev/null || true
        log_info "Removed $old_maintenance_logs old maintenance log files (>14 days)"
        files_removed=$((files_removed + old_maintenance_logs))
    fi

    # Clean cron log files older than 2 weeks
    local old_cron_logs=$(find "$LOG_DIR" -name "cron-*.log" -type f -mtime +14 2>/dev/null | wc -l)
    if [[ $old_cron_logs -gt 0 ]]; then
        find "$LOG_DIR" -name "cron-*.log" -type f -mtime +14 -delete 2>/dev/null || true
        log_info "Removed $old_cron_logs old cron log files (>14 days)"
        files_removed=$((files_removed + old_cron_logs))
    fi

    # Clean validation log files older than 2 weeks
    local old_validation_logs=$(find /tmp -name "streamdb-validation-*.log" -type f -mtime +14 2>/dev/null | wc -l)
    if [[ $old_validation_logs -gt 0 ]]; then
        find /tmp -name "streamdb-validation-*.log" -type f -mtime +14 -delete 2>/dev/null || true
        log_info "Removed $old_validation_logs old validation log files (>14 days)"
        files_removed=$((files_removed + old_validation_logs))
    fi

    log_success "Cleaned up $files_removed old backup and log files (>14 days)"
}

# ============================================================================
# SERVICE STATUS FUNCTIONS
# ============================================================================

check_nginx_status() {
    if systemctl is-active --quiet nginx; then
        log_info "Nginx is running"
        return 0
    else
        log_warn "Nginx is not running"
        return 1
    fi
}

check_nginx_config() {
    if nginx -t >/dev/null 2>&1; then
        log_info "Nginx configuration is valid"
        return 0
    else
        log_error "Nginx configuration is invalid"
        nginx -t
        return 1
    fi
}

check_backend_connectivity() {
    local max_attempts=3
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -s --connect-timeout 10 --max-time 30 "$BACKEND_HEALTH_URL" >/dev/null 2>&1; then
            log_info "Backend server connectivity check passed (attempt $attempt)"
            return 0
        else
            log_warn "Backend server connectivity check failed (attempt $attempt)"
            ((attempt++))
            sleep 5
        fi
    done
    
    log_error "Backend server is not responding after $max_attempts attempts"
    return 1
}

get_service_states() {
    declare -gA SERVICE_STATES
    SERVICE_STATES[nginx]=$(systemctl is-active nginx 2>/dev/null || echo "inactive")
    SERVICE_STATES[fail2ban]=$(systemctl is-active fail2ban 2>/dev/null || echo "inactive")
    SERVICE_STATES[ufw]=$(ufw status | grep -q "Status: active" && echo "active" || echo "inactive")
    
    log_info "Captured service states: nginx=${SERVICE_STATES[nginx]}, fail2ban=${SERVICE_STATES[fail2ban]}, ufw=${SERVICE_STATES[ufw]}"
}

restore_service_states() {
    log_info "Restoring service states..."
    
    # Restore services that were running
    for service in nginx fail2ban; do
        if [[ "${SERVICE_STATES[$service]}" == "active" ]]; then
            if ! systemctl is-active --quiet "$service"; then
                log_warn "Restarting $service (was running before maintenance)"
                systemctl start "$service" || log_error "Failed to restart $service"
            fi
        fi
    done
    
    # Restore UFW if it was active
    if [[ "${SERVICE_STATES[ufw]}" == "active" ]]; then
        if ! ufw status | grep -q "Status: active"; then
            log_warn "Re-enabling UFW (was active before maintenance)"
            ufw --force enable || log_error "Failed to re-enable UFW"
        fi
    fi
}

# ============================================================================
# BACKUP FUNCTIONS
# ============================================================================

create_config_backup() {
    print_section "Creating Configuration Backup"
    
    local backup_file="$BACKUP_DIR/proxy-config-backup-$(date +%Y%m%d-%H%M%S).tar.gz"
    
    # Backup critical configuration files
    tar -czf "$backup_file" \
        "$NGINX_SITES_AVAILABLE/" \
        "$NGINX_SITES_ENABLED/" \
        "$NGINX_MAIN_CONFIG" \
        "$SSL_CERT_PATH" \
        "$SSL_KEY_PATH" \
        /etc/ufw/ \
        /etc/fail2ban/ \
        /etc/crontab \
        /var/spool/cron/crontabs/ \
        2>/dev/null || true
    
    if [[ -f "$backup_file" ]]; then
        log_success "Configuration backup created: $backup_file"
        return 0
    else
        log_error "Failed to create configuration backup"
        return 1
    fi
}

# ============================================================================
# UPDATE FUNCTIONS
# ============================================================================

update_package_lists() {
    print_section "Updating Package Lists"
    
    if apt update; then
        log_success "Package lists updated successfully"
        return 0
    else
        log_error "Failed to update package lists"
        return 1
    fi
}

apply_security_updates() {
    print_section "Applying Security Updates"
    
    # Get list of security updates
    local security_updates=$(apt list --upgradable 2>/dev/null | grep -i security | wc -l)
    
    if [[ $security_updates -gt 0 ]]; then
        log_info "Found $security_updates security updates to apply"
        
        # Apply only security updates
        if unattended-upgrade -d; then
            log_success "Security updates applied successfully"
            return 0
        else
            log_error "Failed to apply security updates"
            return 1
        fi
    else
        log_info "No security updates available"
        return 0
    fi
}

apply_safe_os_updates() {
    print_section "Applying Safe OS Updates"
    
    # Get list of available updates (excluding major version changes)
    local available_updates=$(apt list --upgradable 2>/dev/null | grep -v "WARNING" | wc -l)
    
    if [[ $available_updates -gt 0 ]]; then
        log_info "Found $available_updates updates available"
        
        # Apply updates with safety measures
        DEBIAN_FRONTEND=noninteractive apt-get -y \
            -o Dpkg::Options::="--force-confdef" \
            -o Dpkg::Options::="--force-confold" \
            --no-install-recommends \
            upgrade
        
        if [[ $? -eq 0 ]]; then
            log_success "OS updates applied successfully"
            return 0
        else
            log_error "Failed to apply OS updates"
            return 1
        fi
    else
        log_info "No OS updates available"
        return 0
    fi
}

# ============================================================================
# CLEANUP FUNCTIONS
# ============================================================================

system_cleanup() {
    print_section "Performing System Cleanup"
    
    # Clean package cache
    apt-get clean
    apt-get autoclean
    log_info "Cleaned package cache"
    
    # Remove unused packages
    apt-get -y autoremove
    log_info "Removed unused packages"
    
    # Clean temporary files
    find /tmp -type f -atime +7 -delete 2>/dev/null || true
    find /var/tmp -type f -atime +7 -delete 2>/dev/null || true
    log_info "Cleaned temporary files"
    
    # Rotate and clean logs
    logrotate -f /etc/logrotate.conf 2>/dev/null || true
    log_info "Rotated system logs"
    
    # Clean old kernels (keep current + 1 previous)
    local old_kernels=$(dpkg -l | grep linux-image | grep -v $(uname -r) | awk '{print $2}' | head -n -1)
    if [[ -n "$old_kernels" ]]; then
        echo "$old_kernels" | xargs apt-get -y purge 2>/dev/null || true
        log_info "Cleaned old kernel versions"
    fi
    
    log_success "System cleanup completed"
}

clean_nginx_logs() {
    print_section "Cleaning Nginx Logs"
    
    # Clean Nginx logs older than 30 days
    find /var/log/nginx/ -name "*.log" -type f -mtime +30 -delete 2>/dev/null || true
    find /var/log/nginx/ -name "*.log.*.gz" -type f -mtime +30 -delete 2>/dev/null || true
    log_info "Cleaned old Nginx logs"
    
    # Compress current logs if they're large
    find /var/log/nginx/ -name "*.log" -type f -size +100M -exec gzip {} \; 2>/dev/null || true
    log_info "Compressed large Nginx logs"
    
    log_success "Nginx log cleanup completed"
}

clean_security_logs() {
    print_section "Cleaning Security Logs"
    
    # Clean fail2ban logs older than 14 days
    find /var/log/fail2ban* -type f -mtime +14 -delete 2>/dev/null || true
    log_info "Cleaned old fail2ban logs"
    
    # Clean auth logs older than 30 days
    find /var/log/auth.log* -type f -mtime +30 -delete 2>/dev/null || true
    log_info "Cleaned old auth logs"
    
    log_success "Security log cleanup completed"
}

# ============================================================================
# NGINX MAINTENANCE FUNCTIONS
# ============================================================================

optimize_nginx_config() {
    print_section "Optimizing Nginx Configuration"
    
    # Test current configuration
    if ! check_nginx_config; then
        log_error "Current Nginx configuration is invalid - skipping optimization"
        return 1
    fi
    
    # Reload Nginx to apply any pending changes
    if systemctl reload nginx; then
        log_info "Nginx configuration reloaded successfully"
    else
        log_warn "Failed to reload Nginx configuration"
    fi
    
    log_success "Nginx optimization completed"
}

verify_ssl_certificates() {
    print_section "Verifying SSL Certificates"
    
    # Check if SSL certificate files exist and are readable
    if [[ -f "$SSL_CERT_PATH" && -r "$SSL_CERT_PATH" ]]; then
        log_info "SSL certificate file is accessible"
    else
        log_error "SSL certificate file is missing or not readable: $SSL_CERT_PATH"
        return 1
    fi
    
    if [[ -f "$SSL_KEY_PATH" && -r "$SSL_KEY_PATH" ]]; then
        log_info "SSL private key file is accessible"
    else
        log_error "SSL private key file is missing or not readable: $SSL_KEY_PATH"
        return 1
    fi
    
    # Check certificate expiration (Cloudflare origin certificates are long-lived)
    local cert_expiry=$(openssl x509 -in "$SSL_CERT_PATH" -noout -enddate 2>/dev/null | cut -d= -f2)
    if [[ -n "$cert_expiry" ]]; then
        log_info "SSL certificate expires: $cert_expiry"
    else
        log_warn "Could not determine SSL certificate expiration"
    fi
    
    log_success "SSL certificate verification completed"
}

# ============================================================================
# HEALTH CHECK FUNCTIONS
# ============================================================================

health_check() {
    print_section "Performing Health Check"
    
    local health_status=0
    
    # Check Nginx service
    if ! check_nginx_status; then
        health_status=1
    fi
    
    # Check Nginx configuration
    if ! check_nginx_config; then
        health_status=1
    fi
    
    # Check backend connectivity
    if ! check_backend_connectivity; then
        health_status=1
    fi
    
    # Check disk space
    local disk_usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    if [[ $disk_usage -gt 85 ]]; then
        log_warn "Disk usage is high: ${disk_usage}%"
        health_status=1
    else
        log_info "Disk usage is acceptable: ${disk_usage}%"
    fi
    
    # Check memory usage
    local mem_usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    if [[ $mem_usage -gt 90 ]]; then
        log_warn "Memory usage is high: ${mem_usage}%"
        health_status=1
    else
        log_info "Memory usage is acceptable: ${mem_usage}%"
    fi
    
    # Test external connectivity to main domains
    for domain in "streamdb.online" "fastpanel.streamdb.online"; do
        if curl -s -o /dev/null -w "%{http_code}" "https://$domain" | grep -q "200\|301\|302"; then
            log_info "External connectivity to $domain is working"
        else
            log_warn "External connectivity to $domain failed"
            health_status=1
        fi
    done
    
    # Check firewall status
    if ufw status | grep -q "Status: active"; then
        log_info "UFW firewall is active"
    else
        log_warn "UFW firewall is not active"
        health_status=1
    fi
    
    if [[ $health_status -eq 0 ]]; then
        log_success "All health checks passed"
        return 0
    else
        log_warn "Some health checks failed"
        return 1
    fi
}

# ============================================================================
# MAIN EXECUTION
# ============================================================================

main() {
    # Setup
    check_root
    setup_directories
    create_lock
    
    # Trap to ensure cleanup on exit
    trap 'remove_lock; exit' INT TERM EXIT
    
    print_header "$SCRIPT_NAME v$SCRIPT_VERSION - $(date)"
    log_info "Starting maintenance on backend2ndrevproxy (*************)"
    
    # Wait for backend server maintenance to complete (if running)
    log_info "Waiting 5 minutes for backend server maintenance to complete..."
    sleep 300
    
    # Capture current service states
    get_service_states
    
    # Create backups
    if ! create_config_backup; then
        log_error "Configuration backup failed - aborting maintenance"
        exit 1
    fi
    
    # Verify backend server is responsive before proceeding
    if ! check_backend_connectivity; then
        log_error "Backend server is not responsive - aborting maintenance"
        exit 1
    fi
    
    # Perform updates
    update_package_lists
    apply_security_updates
    apply_safe_os_updates
    
    # Perform cleanup
    system_cleanup
    clean_nginx_logs
    clean_security_logs
    cleanup_old_logs
    cleanup_old_backups_and_logs
    
    # Nginx maintenance
    verify_ssl_certificates
    optimize_nginx_config
    
    # Restore services if needed
    restore_service_states
    
    # Final health check
    sleep 10  # Give services time to stabilize
    health_check
    
    print_header "Maintenance Completed Successfully"
    log_success "Reverse proxy server maintenance completed at $(date)"
    
    # Remove lock file
    remove_lock
}

# Execute main function
main "$@"
